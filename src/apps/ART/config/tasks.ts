import HIVClinicRegistration from "@/apps/ART/components/ARTRegistration/HIVClinicRegistration.vue";
import Reception from "@/apps/ART/components/Reception/Reception.vue";
import Vitals from "@/apps/ART/components/Vitals.vue";
import Staging from "@/apps/ART/components/Staging.vue";
import Consultation from "@/apps/ART/components/Consultation.vue";
import ARTAdherence from "@/apps/ART/components/ARTAdherence.vue";
import Prescription from "@/apps/ART/components/Prescription.vue";
import ARTAppointment from "@/apps/ART/components/ARTAppointment.vue";
import ARTPatientType from "@/apps/ART/components/ARTPatientType.vue";
import EmergencyRefill from "../components/EmergencyRefill.vue";
import ArtFormWrapper from "../components/ArtFormWrapper.vue";
import ArtDispensation from "../components/ArtDispensation.vue";
import { PatientService } from "@/services/patient_service";
import {
    clipboard,
    reader,
    heart,
    documentText,
    apps,
    medical,
    fileTrayFull,
    person,
    calendar,
    play,
    barcode,
    print,
    folderOpen,
    card,
} from "ionicons/icons";
import { modal } from "@/utils/modal";
import { createModal } from "@/utils/Alerts";
import router from "@/router";
import ArtClinicRegistration from "../components/ArtClinicRegistration.vue";
import ArtOutcome from "../components/ArtOutcome.vue";
import { printArtPatientDemographicsLbl, printArtVisitLbl, printNpidLbl } from "../Labels";
import LabResults from "../components/LabResults.vue";

function buildFormTask(component: any, title: string, icon: any) {
    return {
        icon,
        label: title,
        action: () => {
            createModal(ArtFormWrapper, {}, false, {
                title,
                useComponent: component,
                onClose: modal.hide,
            });
        },
    };
}

export default {
    tasks: [
        {
            label: "Start ART Workflow",
            icon: play,
            iconColor: "warning",
            action: () => {
                router.push("/ARTVisit");
            },
        },
        buildFormTask(ArtClinicRegistration, "HIV clinic registration", clipboard),
        buildFormTask(Reception, "HIV reception", reader),
        buildFormTask(Vitals, "Vitals", heart),
        buildFormTask(Staging, "HIV staging", documentText),
        buildFormTask(Consultation, "HIV clinic consultation", documentText),
        buildFormTask(ARTAdherence, "ART Adherence", apps),
        buildFormTask(Prescription, "Treatment", medical),
        buildFormTask(ArtDispensation, "Dispense drugs", medical),
        buildFormTask(ARTAppointment, "Manage appointments", calendar),
        buildFormTask(ARTPatientType, "Change patient type", person),
        buildFormTask(ARTPatientType, "Change patient type", person),
    ],
    other: [
        {
            label: "Mastercard",
            icon: card,
            action: () => router.push("/art/mastercard"),
        },
        buildFormTask(LabResults, "Lab Results", fileTrayFull),
        buildFormTask(EmergencyRefill, "Emergency Refill", fileTrayFull),
        buildFormTask(ArtOutcome, "Program outcome", fileTrayFull),
        {
            label: "NATIONAL HEALTH ID (PRINT)",
            icon: barcode,
            action: async () => {
                await printNpidLbl(new PatientService().getID());
            },
        },
        {
            label: "DEMOGRAPHICS (PRINT)",
            icon: print,
            action: () => {
                printArtPatientDemographicsLbl(new PatientService().getID());
            },
        },
        {
            label: "VISIT SUMMARY (PRINT)",
            icon: folderOpen,
            action: () => {
                printArtVisitLbl(new PatientService().getID());
            },
        },
    ],
};
