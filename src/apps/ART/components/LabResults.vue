<template>
    <div>
        <ion-card>
            <ion-card-content>
                <div class="ion-text-center">
                    <ion-icon style= :icon="medkitOutline"></ion-icon>
                    <h1>No tests found</h1>
                </div>
            </ion-card-content>
        </ion-card>
        <ion-accordion-group ref="accordionGroup" class="previousView" value="first">
            <ion-accordion value="first" toggle-icon-slot="start" class="custom_card">
                <ion-item slot="header" color="light">
                    <ion-label class="ion-padding">
                        <b>XLKB24AM1</b>
                        <p>
                            HIV Viral load | 12th May 2025
                        </p>
                    </ion-label>
                    <ion-chip style="background: green; color: white;">
                        <ion-icon color="light" :icon="checkmarkCircle"></ion-icon>
                        <b style="padding: 2px;">Completed</b>
                    </ion-chip>
                    <ion-chip style="background: red; color: white;">
                        <ion-icon color="light" :icon="closeCircle"></ion-icon>
                        <b style="padding: 2px;">Incomplete</b>
                    </ion-chip>
                </ion-item>
                <div class="ion-padding" slot="content" style="margin-bottom: 125px">
                    <span>
                        
                    </span>
                </div>
            </ion-accordion>
        </ion-accordion-group>
    </div>
</template>
<script lang="ts" setup>
import {
    IonChip,
    IonIcon,
    IonAccordion,
    IonAccordionGroup,
    IonItem,
    IonLabel
} from "@ionic/vue"
import { checkmarkCircle, closeCircle, medkitOutline } from "ionicons/icons";
</script>
