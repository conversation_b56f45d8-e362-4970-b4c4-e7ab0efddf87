<template>
    <div>
        <ion-card style="height: 40vh;">
            <ion-card-content>
                <div class="ion-text-center" style="margin-top: 15%;">
                    <ion-icon style="font-size: 5rem!important;" :icon="medkitOutline"></ion-icon>
                    <h1>No tests found</h1>
                </div>
            </ion-card-content>
        </ion-card>
        <ion-accordion-group ref="accordionGroup" class="previousView" value="first">
            <ion-accordion value="first" toggle-icon-slot="start" class="custom_card">
                <ion-item slot="header" color="light">
                    <ion-label class="ion-padding">
                        <b>XLKB24AM1</b>
                        <p>
                            HIV Viral load | <b>12th May 2025</b>
                        </p>
                    </ion-label>
                    <ion-chip style="background: green; color: white;">
                        <ion-icon color="light" :icon="checkmarkCircle"></ion-icon>
                        <b style="padding: 2px;">Completed</b>
                    </ion-chip>
                    <ion-chip style="background: red; color: white;">
                        <ion-icon color="light" :icon="closeCircle"></ion-icon>
                        <b style="padding: 2px;">Incomplete</b>
                    </ion-chip>
                </ion-item>
                <div class="ion-padding" slot="content" style="margin-bottom: 125px">
                    <!-- Lab Results Form -->
                    <ion-grid>
                        <!-- Error Message -->
                        <ion-row v-if="errorMessage">
                            <ion-col size="12">
                                <ion-note color="danger">{{ errorMessage }}</ion-note>
                            </ion-col>
                        </ion-row>

                        <!-- Date Field -->
                        <ion-row>
                            <ion-col>
                                <ion-label>Date of Lab Result <span style="color: red">*</span></ion-label>
                                <ion-datetime
                                    presentation="date"
                                    size="cover"
                                    v-model="labResultForm.date"
                                    :max="maxDate"
                                    style="border: 1px solid #ccc; border-radius: 4px; padding: 8px; margin-top: 8px;"
                                />
                            </ion-col>
                        </ion-row>

                        <!-- Lab Indicators Multi-Select -->
                        <ion-row>
                            <ion-col size="12">
                                <ion-label>Lab Result Indicators <span style="color: red">*</span></ion-label>
                                <div style="margin-top: 8px;">
                                    <VueMultiselect
                                        v-model="labResultForm.selectedIndicators"
                                        :multiple="true"
                                        :taggable="false"
                                        :hide-selected="true"
                                        :close-on-select="false"
                                        openDirection="bottom"
                                        placeholder="Select lab result indicators"
                                        selectLabel=""
                                        label="name"
                                        track-by="id"
                                        :searchable="true"
                                        :options="labIndicators"
                                    />
                                </div>
                            </ion-col>
                        </ion-row>

                        <!-- Results Field -->
                        <ion-row>
                            <ion-col size="12">
                                <ion-label>Results <span style="color: red">*</span></ion-label>
                                <ion-textarea
                                    v-model="labResultForm.results"
                                    placeholder="Enter lab results..."
                                    :rows="4"
                                    style="margin-top: 8px;"
                                />
                            </ion-col>
                        </ion-row>

                        <!-- Save Button -->
                        <ion-row>
                            <ion-col size="12" class="ion-text-center">
                                <ion-button
                                    @click="saveLabResult"
                                    :disabled="isLoading || !isFormValid"
                                    color="primary"
                                >
                                    <ion-spinner v-if="isLoading" name="crescent" style="margin-right: 8px;"></ion-spinner>
                                    {{ isLoading ? 'Saving...' : 'Save Lab Result' }}
                                </ion-button>
                            </ion-col>
                        </ion-row>
                    </ion-grid>
                </div>
            </ion-accordion>
        </ion-accordion-group>
    </div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import {
    IonRow,
    IonCol,
    IonGrid,
    IonCard,
    IonCardContent,
    IonChip,
    IonIcon,
    IonAccordion,
    IonAccordionGroup,
    IonItem,
    IonLabel,
    IonNote,
    IonDatetime,
    IonTextarea,
    IonButton,
    IonSpinner
} from "@ionic/vue"
import { checkmarkCircle, closeCircle, medkitOutline } from "ionicons/icons";
import VueMultiselect from 'vue-multiselect';
import { toastSuccess, toastDanger } from '@/utils/Alerts';
import { Service } from '@/services/service';

// Reactive data
const isLoading = ref(false);
const errorMessage = ref('');

// Form data
const labResultForm = ref({
    date: '',
    selectedIndicators: [] as Array<{id: string, name: string}>,
    results: ''
});

// Max date (today)
const maxDate = computed(() => {
    return new Date().toISOString();
});

// Lab indicators options
const labIndicators = ref([
    { id: '1', name: 'HIV Viral Load' },
    { id: '2', name: 'CD4 Count' },
    { id: '3', name: 'Hemoglobin' },
    { id: '4', name: 'White Blood Cell Count' },
    { id: '5', name: 'Platelet Count' },
    { id: '6', name: 'Creatinine' },
    { id: '7', name: 'ALT (Alanine Aminotransferase)' },
    { id: '8', name: 'AST (Aspartate Aminotransferase)' },
    { id: '9', name: 'Total Bilirubin' },
    { id: '10', name: 'Glucose' },
    { id: '11', name: 'Cholesterol' },
    { id: '12', name: 'Triglycerides' },
    { id: '13', name: 'Hepatitis B Surface Antigen' },
    { id: '14', name: 'Hepatitis C Antibody' },
    { id: '15', name: 'Tuberculosis Test' }
]);

// Form validation
const isFormValid = computed(() => {
    return labResultForm.value.date &&
           labResultForm.value.selectedIndicators.length > 0 &&
           labResultForm.value.results.trim();
});

// Methods
const saveLabResult = async () => {
    try {
        isLoading.value = true;
        errorMessage.value = '';

        // Validate form
        if (!isFormValid.value) {
            errorMessage.value = 'Please fill in all required fields';
            return;
        }

        // Prepare data for saving
        const labResultData = {
            date: labResultForm.value.date,
            indicators: labResultForm.value.selectedIndicators.map(indicator => indicator.name).join(', '),
            results: labResultForm.value.results,
            timestamp: new Date().toISOString()
        };

        // TODO: Implement actual save logic here
        // This would typically involve calling a service to save to the backend
        console.log('Saving lab result:', labResultData);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        toastSuccess('Lab result saved successfully');

        // Reset form
        labResultForm.value = {
            date: '',
            selectedIndicators: [],
            results: ''
        };

    } catch (error) {
        console.error('Error saving lab result:', error);
        errorMessage.value = 'Failed to save lab result. Please try again.';
        toastDanger('Failed to save lab result');
    } finally {
        isLoading.value = false;
    }
};

// Initialize form with today's date
onMounted(() => {
    labResultForm.value.date = Service.getSessionDate();
});
</script>

<style>
@import 'vue-multiselect/dist/vue-multiselect.css';
</style>
